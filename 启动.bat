@echo off
echo ========================================
echo        Mijia Project Launcher
echo ========================================
echo.

:: Check if virtual environment exists
if not exist "venv" (
    echo [ERROR] Virtual environment does not exist!
    echo [TIP] Please run setup_venv.bat first to create virtual environment
    pause
    exit /b 1
)

:: Check if app.py exists
if not exist "app.py" (
    echo [ERROR] app.py file does not exist!
    echo [TIP] Please ensure you are running this script in the project root directory
    pause
    exit /b 1
)

:: Activate virtual environment
echo [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

if %errorlevel% neq 0 (
    echo [ERROR] Virtual environment activation failed
    echo [TIP] Please re-run setup_venv.bat to rebuild virtual environment
    pause
    exit /b 1
)

echo [INFO] Virtual environment activated
echo [INFO] Python version:
python --version
echo.

:: Check if dependencies are installed
echo [INFO] Checking project dependencies...
python -c "import flask, requests, mijiaAPI" >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Some dependencies may not be installed, trying to install...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] Dependency installation failed
        pause
        exit /b 1
    )
)

echo [INFO] Dependency check completed
echo.

:: Start application
echo ========================================
echo           Starting Flask Application
echo ========================================
echo [INFO] Starting app.py...
echo [TIP] Press Ctrl+C to stop the application
echo [TIP] Browser will open automatically after startup
echo ========================================
echo.

python app.py

:: Handle application exit
echo.
echo ========================================
echo [INFO] Application stopped
echo ========================================
pause
