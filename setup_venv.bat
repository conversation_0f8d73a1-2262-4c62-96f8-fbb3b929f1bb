@echo off
echo ========================================
echo        Mijia Project Virtual Environment Setup
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not detected, please install Python 3.7 or higher
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [INFO] Python version detected:
python --version

:: Check if pip is available
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] pip not found, please ensure Python is installed correctly
    pause
    exit /b 1
)

echo [INFO] pip version detected:
pip --version
echo.

:: Create virtual environment
echo [STEP 1] Creating virtual environment...
if exist "venv" (
    echo [WARNING] Virtual environment directory already exists
    set /p choice="Enter y to delete and recreate, any other key to skip: "
    if /i "%choice%"=="y" (
        echo [INFO] Removing existing virtual environment...
        rmdir /s /q venv
        echo [INFO] Creating new virtual environment...
        python -m venv venv
    ) else (
        echo [INFO] Skipping virtual environment creation
    )
) else (
    echo [INFO] Creating virtual environment...
    python -m venv venv
)

if %errorlevel% neq 0 (
    echo [ERROR] Virtual environment creation failed
    pause
    exit /b 1
)

echo [INFO] Virtual environment created successfully
echo.

:: Activate virtual environment and install dependencies
echo [STEP 2] Activating virtual environment and installing dependencies...
call venv\Scripts\activate.bat

if %errorlevel% neq 0 (
    echo [ERROR] Virtual environment activation failed
    pause
    exit /b 1
)

echo [INFO] Virtual environment activated
echo [INFO] Upgrading pip to latest version...
python -m pip install --upgrade pip

echo [INFO] Installing project dependencies...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo [ERROR] Dependency installation failed, check requirements.txt and network
    pause
    exit /b 1
)

echo.
echo ========================================
echo           Setup Complete!
echo ========================================
echo [SUCCESS] Virtual environment created and all dependencies installed
echo [TIP] Use start_app.bat to launch the application
echo [TIP] Manual activation: venv\Scripts\activate.bat
echo ========================================
echo.
pause
