# --- 导入模块和基础设置 ---
import json
import os
import time
import threading
import logging
import webbrowser
import shutil
from flask import Flask, request, jsonify, render_template_string, send_from_directory
from configparser import ConfigParser, NoOptionError
from mijiaAPI import mijiaAPI, mijiaLogin

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 启动时清理缓存 ---
def cleanup_cache():
    """清理启动前的缓存文件"""
    try:
        # 删除二维码文件
        if os.path.exists('qr.png'):
            os.remove('qr.png')
            logging.info("已清理二维码文件: qr.png")

        # 清理jsons目录中的所有文件
        if os.path.exists('jsons'):
            for filename in os.listdir('jsons'):
                file_path = os.path.join('jsons', filename)
                # 只删除文件，不删除子目录
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    logging.info(f"已清理缓存文件: {file_path}")

        logging.info("缓存清理完成")
    except Exception as e:
        logging.error(f"清理缓存时发生错误: {e}")

# 执行缓存清理
cleanup_cache()

# --- 加载配置文件 ---
config = ConfigParser()
config.read('config.ini', encoding='utf-8')
SERVER_HOST = config.get('server', 'host')
SERVER_PORT = config.getint('server', 'port')
AUTH_CHECK_INTERVAL = config.getint('mijia', 'check_interval_seconds')
AUTH_JSON_PATH = 'jsons/auth.json'

# --- 可选的默认家庭名称配置（向后兼容） ---
try:
    DEFAULT_HOME_NAME = config.get('mijia', 'home_name')
except NoOptionError:
    DEFAULT_HOME_NAME = None  # 不再强制要求，支持多家庭模式

# --- 全局状态变量 ---
app = Flask(__name__)
api_client = None
auth_status = "initializing"  # "initializing", "need_login", "logging_in", "ok", "expired"
last_auth_check_time = None
auth_file_last_modified = 0
name_to_id_cache = {}  # {home_name: {scene_name: scene_id}}
homes_list = []  # 存储所有家庭信息
login_api = None  # 用于二维码登录的API实例
qr_code_data = None  # 存储二维码数据
lock = threading.Lock()

# --- 核心逻辑函数 (refresh_cache_logic, initialize_api_and_cache, auth_polling_thread) ---
# 这些函数无需任何修改，它们的逻辑是通用的。
def refresh_cache_logic():
    """
    获取所有家庭和场景信息，以构建名称到ID的映射缓存。
    此函数应在获取锁之后调用。
    成功返回 True, 失败返回 False。
    """
    global name_to_id_cache, homes_list, api_client
    if not api_client:
        return False

    logging.info("正在尝试刷新缓存...")
    try:
        new_cache = {}
        homes = api_client.get_homes_list()
        if not homes:
            logging.warning("无法获取家庭列表，或未找到任何家庭。")
            return False

        # 更新家庭列表
        homes_list = homes

        for home in homes:
            home_id = home.get('id')
            home_name = home.get('name')
            if not home_id or not home_name:
                continue

            new_cache[home_name] = {}
            scenes = api_client.get_scenes_list(home_id)
            if scenes:
                for scene in scenes:
                    scene_name = scene.get('name')
                    scene_id = scene.get('scene_id')
                    if scene_name and scene_id:
                        new_cache[home_name][scene_name] = scene_id

        name_to_id_cache = new_cache
        logging.info(f"缓存刷新成功。共找到 {len(homes)} 个家庭，总计 {sum(len(scenes) for scenes in new_cache.values())} 个场景。")
        return True
    except Exception as e:
        logging.error(f"刷新缓存失败: {e}")
        # 刷新缓存失败，可能是因为认证信息过期，后台轮询线程会处理状态变更
        return False

def initialize_api_and_cache():
    """
    初始化 mijiaAPI 客户端并执行首次缓存构建。
    这是服务启动或从故障中恢复的核心函数。
    成功返回 True, 失败返回 False。
    """
    global api_client, auth_status, auth_file_last_modified

    with lock:
        logging.info("正在尝试初始化 API 客户端并构建缓存...")
        if not os.path.exists(AUTH_JSON_PATH):
            logging.info(f"文件 {AUTH_JSON_PATH} 未找到。需要用户登录。")
            auth_status = "need_login"
            return False

        try:
            with open(AUTH_JSON_PATH, 'r', encoding='utf-8') as f:
                auth_data = json.load(f)

            # 初始化客户端
            api_client = mijiaAPI(auth_data)

            # 执行初次缓存构建，如果失败，则整个初始化失败
            if refresh_cache_logic():
                auth_status = "ok"
                auth_file_last_modified = os.path.getmtime(AUTH_JSON_PATH)
                home_count = len(homes_list)
                logging.info(f"API 客户端初始化成功，缓存已成功构建。找到 {home_count} 个家庭。")
                return True
            else:
                # 如果缓存构建失败，很可能是认证问题
                auth_status = "expired"
                logging.error("在初始化过程中构建缓存失败。将认证状态设置为 '已过期'。")
                return False

        except Exception as e:
            logging.error(f"初始化失败: {e}")
            auth_status = "expired"
            api_client = None
            return False

def auth_polling_thread():
    """
    后台轮询线程，主动检查认证状态并处理自动恢复逻辑。
    """
    global auth_status, last_auth_check_time, api_client

    while True:
        time.sleep(AUTH_CHECK_INTERVAL)
        logging.info("后台线程正在执行认证状态检查...")

        with lock:
            if auth_status in ["expired", "need_login", "logging_in"]:
                try:
                    # 检查用户是否已更新 auth.json 文件
                    if os.path.exists(AUTH_JSON_PATH):
                        current_mtime = os.path.getmtime(AUTH_JSON_PATH)
                        if current_mtime > auth_file_last_modified:
                            logging.info(f"检测到 {AUTH_JSON_PATH} 文件已更新。正在尝试自动恢复连接...")
                            initialize_api_and_cache()
                    else:
                        if auth_status == "expired":
                            auth_status = "need_login"
                        elif auth_status != "logging_in":
                            logging.debug(f"仍在等待 {AUTH_JSON_PATH} 文件的创建...")
                except Exception as e:
                    logging.error(f"自动恢复过程中发生错误: {e}")

            elif auth_status == "ok" and api_client:
                # 执行一次轻量级的API调用来检查Token是否仍然有效
                try:
                    # get_homes_list 是一个很好的、低开销的检查方法
                    api_client.get_homes_list()
                    logging.info("认证检查成功，Token 仍然有效。")
                except Exception as e:
                    logging.error(f"认证检查失败，Token 可能已过期: {e}")
                    auth_status = "expired"
                    api_client = None

            last_auth_check_time = time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime())

# --- API 端点 ---

@app.route('/', methods=['GET'])
def index():
    """主页面 - 返回Web界面"""
    with open('control_desk.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    return html_content

@app.route('/status', methods=['GET'])
def get_status():
    return jsonify({
        "service_status": "running",
        "authentication_status": auth_status,
        "default_home_name": DEFAULT_HOME_NAME,
        "last_check_time": last_auth_check_time,
        "homes_count": len(homes_list) if homes_list else 0
    })

@app.route('/homes', methods=['GET'])
def get_homes():
    """获取所有家庭列表"""
    if auth_status != 'ok':
        return jsonify({"status": "error", "message": "服务尚未就绪，认证状态不正常。"}), 503

    return jsonify([{"id": home.get('id'), "name": home.get('name')} for home in homes_list])

@app.route('/scenes', methods=['GET'])
def get_scenes():
    """获取指定家庭下的场景列表"""
    if auth_status != 'ok':
        return jsonify({"status": "error", "message": "服务尚未就绪，认证状态不正常。"}), 503

    home_name = request.args.get('home_name')

    # 如果没有指定家庭名称，使用默认家庭或返回所有家庭的场景
    if not home_name:
        if DEFAULT_HOME_NAME:
            home_name = DEFAULT_HOME_NAME
        else:
            # 返回所有家庭的场景
            return jsonify(name_to_id_cache)

    # 从缓存中获取指定家庭的场景
    scenes_in_home = name_to_id_cache.get(home_name)

    if scenes_in_home is None:
        return jsonify({"status": "error", "message": f"在缓存中未找到家庭 '{home_name}'。"}), 404

    # 返回场景列表，包含名称和URL
    scenes_list = []
    for scene_name in scenes_in_home.keys():
        scenes_list.append({
            "name": scene_name,
            "url": f"http://{SERVER_HOST}:{SERVER_PORT}/run_scene/{scene_name}?home_name={home_name}"
        })

    return jsonify(scenes_list)

@app.route('/refresh', methods=['POST'])
def refresh_scenes():
    """手动刷新缓存"""
    if auth_status != 'ok':
        return jsonify({"status": "error", "message": "无法刷新，认证状态不正常。"}), 401

    with lock:
        if refresh_cache_logic():
            return jsonify({"status": "success", "message": "缓存已成功刷新。"})
        else:
            return jsonify({"status": "error", "message": "刷新缓存失败，请检查日志获取详细信息。"}), 500

@app.route('/login/start', methods=['POST'])
def start_login():
    """启动登录流程 - 在后台线程中执行QR登录"""
    global auth_status

    if auth_status == "ok":
        return jsonify({"status": "already_logged_in", "message": "已经登录"})

    def login_thread():
        global auth_status
        try:
            logging.info("开始二维码登录流程...")
            auth_status = "logging_in"

            login_api = mijiaLogin()
            auth_data = login_api.QRlogin()

            if auth_data:
                # 保存认证数据
                if not os.path.exists('jsons'):
                    os.makedirs('jsons')

                with open(AUTH_JSON_PATH, 'w', encoding='utf-8') as f:
                    json.dump(auth_data, f, indent=2, ensure_ascii=False)

                # 重新初始化API客户端
                initialize_api_and_cache()
                logging.info("登录成功，API客户端已初始化")
            else:
                auth_status = "need_login"
                logging.error("登录失败")

        except Exception as e:
            logging.error(f"登录过程中发生错误: {e}")
            auth_status = "need_login"

    # 在后台线程中启动登录
    threading.Thread(target=login_thread, daemon=True).start()
    auth_status = "logging_in"

    return jsonify({
        "status": "started",
        "message": "登录流程已启动，请使用米家APP扫描二维码"
    })

@app.route('/qr.png', methods=['GET'])
def get_qr_code():
    """获取二维码图片"""
    qr_path = 'qr.png'
    if os.path.exists(qr_path):
        return send_from_directory('.', 'qr.png')
    else:
        return jsonify({"error": "二维码文件不存在"}), 404

@app.route('/qr/status', methods=['GET'])
def get_qr_status():
    """检查二维码是否已生成"""
    qr_path = 'qr.png'
    return jsonify({
        "exists": os.path.exists(qr_path),
        "auth_status": auth_status
    })

@app.route('/run_scene/<scene_name>', methods=['GET'])
def run_scene(scene_name):
    """执行指定的场景"""
    if auth_status != 'ok':
        return jsonify({"status": "error", "message": "认证已过期，请重新登录。"}), 401

    home_name = request.args.get('home_name')

    # 如果没有指定家庭名称，使用默认家庭
    if not home_name:
        if DEFAULT_HOME_NAME:
            home_name = DEFAULT_HOME_NAME
        else:
            return jsonify({"status": "error", "message": "请指定家庭名称。"}), 400

    try:
        # 查找场景ID
        scene_id = name_to_id_cache[home_name][scene_name]
    except KeyError:
        return jsonify({"status": "error", "message": f"未找到场景：无法在家庭 '{home_name}' 中找到名为 '{scene_name}' 的场景。"}), 404

    with lock:
        if not api_client:
            return jsonify({"status": "error", "message": "API 客户端不可用。"}), 500
        try:
            ret = api_client.run_scene(scene_id)
            logging.info(f"运行场景 '{scene_name}' (家庭: {home_name})，返回结果: {ret}")
            return jsonify({"status": "success", "message": f"场景 '{scene_name}' 在家庭 '{home_name}' 中已成功执行。", "result": ret})
        except Exception as e:
            logging.error(f"运行场景 '{scene_name}' 失败: {e}")
            return jsonify({"status": "error", "message": f"执行场景时发生错误，原因: {e}"}), 500

# --- 主程序入口 ---

if __name__ == '__main__':
    initialize_api_and_cache()

    polling = threading.Thread(target=auth_polling_thread, daemon=True)
    polling.start()

    server_url = f"http://{SERVER_HOST}:{SERVER_PORT}"
    logging.info(f"服务已启动，监听地址 {server_url}")

    # 自动打开浏览器
    def open_browser():
        time.sleep(1)  # 等待服务器启动
        try:
            webbrowser.open(server_url)
            logging.info(f"已自动打开浏览器: {server_url}")
        except Exception as e:
            logging.warning(f"无法自动打开浏览器: {e}")

    threading.Thread(target=open_browser, daemon=True).start()

    app.run(host=SERVER_HOST, port=SERVER_PORT, debug=False)